# 🚀 Base64 Streaming MCP Server - <PERSON>llanıcı Kurulum Rehberi

Bu rehber, base64-streaming-mcp server'ını sisteminize kurmak ve Claude Desktop ile entegre etmek için gereken tüm adımları içerir.

## 📋 Sistem Gereksinimleri

### Minimum Gereksinimler
- **Python**: 3.7 veya üzeri
- **<PERSON><PERSON><PERSON><PERSON> Sistemi**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **RAM**: En az 4GB (8GB önerilir)
- **Disk Alanı**: 500MB boş alan
- **İnternet**: <PERSON><PERSON><PERSON> için gerekli

### Platform-Specific Gereksinimler

#### Windows
- Windows 10 veya Windows 11
- Python 3.7+ (Microsoft Store'dan veya python.org'dan)
- Ekran kaydı izinleri

#### macOS
- macOS 10.14 (Mojave) veya üzeri
- Python 3.7+ (Homebrew önerilir)
- **ÖNEMLİ**: <PERSON>kran kaydı izinleri gerekli (System Preferences > Security & Privacy > Privacy > Screen Recording)

#### Linux
- Ubuntu 18.04+, Debian 10+, CentOS 8+, Fedora 30+
- Python 3.7+ ve pip
- X11 display server
- Gerekli sistem paketleri: `python3-dev`, `python3-pip`

## 🔧 Kurulum Yöntemleri

### Yöntem 1: Pip ile Kurulum (Önerilen)

#### Adım 1: Python ve Pip Kontrolü
```bash
# Python versiyonunu kontrol edin
python --version
# veya
python3 --version

# Pip versiyonunu kontrol edin
pip --version
# veya
pip3 --version
```

#### Adım 2: Paketi Kurun
```bash
# GitHub'dan doğrudan kurulum
pip install git+https://github.com/inkbytefo/base64-streaming-mcp.git

# Veya PyPI'den (yayınlandığında)
pip install base64-streaming-mcp
```

#### Adım 3: Kurulumu Test Edin
```bash
# CLI komutunun çalıştığını kontrol edin
base64-streaming-mcp --help

# Versiyon kontrolü
base64-streaming-mcp --version
```

### Yöntem 2: Kaynak Koddan Kurulum

#### Adım 1: Repository'yi İndirin
```bash
# Git ile klonlama
git clone https://github.com/inkbytefo/base64-streaming-mcp.git
cd base64-streaming-mcp

# Veya ZIP dosyası indirip açın
```

#### Adım 2: Bağımlılıkları Kurun
```bash
# Geliştirme modunda kurulum
pip install -e .

# Veya sadece bağımlılıkları
pip install -r requirements.txt
```

#### Adım 3: Test Edin
```bash
# Test scriptini çalıştırın
python test_installation.py

# Sunucuyu başlatın
python run_server.py --help
```

## 🖥️ Claude Desktop Entegrasyonu

### Adım 1: Claude Desktop Config Dosyasını Bulun

#### Windows
```
%APPDATA%\Claude\claude_desktop_config.json
```

#### macOS
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

#### Linux
```
~/.config/Claude/claude_desktop_config.json
```

### Adım 2: Konfigürasyon Dosyasını Düzenleyin

#### Temel Konfigürasyon (Pip Kurulumu)
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--host", "localhost", "--port", "8080"]
    }
  }
}
```

#### Gelişmiş Konfigürasyon
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": [
        "--host", "localhost",
        "--port", "8080",
        "--fps", "30",
        "--quality", "85",
        "--compression", "gzip",
        "--log-level", "INFO"
      ],
      "env": {
        "PYTHONPATH": "/path/to/your/project"
      }
    }
  }
}
```

#### Kaynak Kod Kurulumu için Konfigürasyon
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "python",
      "args": ["run_server.py"],
      "cwd": "/tam/yol/base64-streaming-mcp"
    }
  }
}
```

### Platform-Specific Örnekler

#### Windows Örneği
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--host", "localhost", "--port", "8080"],
      "env": {
        "PATH": "C:\\Python39\\Scripts;%PATH%"
      }
    }
  }
}
```

#### macOS Örneği
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "/usr/local/bin/base64-streaming-mcp",
      "args": ["--host", "localhost", "--port", "8080"]
    }
  }
}
```

#### Linux Örneği
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "/home/<USER>/.local/bin/base64-streaming-mcp",
      "args": ["--host", "localhost", "--port", "8080"]
    }
  }
}
```

## 🔧 Konfigürasyon Seçenekleri

### Komut Satırı Parametreleri
```bash
base64-streaming-mcp [SEÇENEKLER]

Seçenekler:
  --host TEXT              Sunucu adresi [varsayılan: localhost]
  --port INTEGER           Port numarası [varsayılan: 8080]
  --fps INTEGER            Saniyedeki kare sayısı [varsayılan: 30]
  --quality INTEGER        JPEG kalitesi 1-100 [varsayılan: 85]
  --compression TEXT       Sıkıştırma türü (none, gzip, zstd) [varsayılan: gzip]
  --config PATH            Konfigürasyon dosyası yolu (JSON formatı)
  --log-level TEXT         Log seviyesi (DEBUG, INFO, WARNING, ERROR) [varsayılan: INFO]
  --version                Versiyon bilgisini göster
  --help                   Yardım mesajını göster
```

### Konfigürasyon Dosyası (config.json)
```json
{
  "host": "localhost",
  "port": 8080,
  "transport_type": "streamable_http",
  "fps": 30,
  "compression": "gzip",
  "image_format": "jpeg",
  "jpeg_quality": 85,
  "max_width": 1920,
  "max_height": 1080,
  "enable_compression": true,
  "chunk_size": 65536,
  "log_level": "INFO"
}
```

## 🚀 İlk Çalıştırma

### Adım 1: Sunucuyu Manuel Test Edin
```bash
# Sunucuyu başlatın
base64-streaming-mcp --host localhost --port 8080

# Başka bir terminalde test edin
curl http://localhost:8080/health
```

### Adım 2: Claude Desktop'ı Yeniden Başlatın
1. Claude Desktop'ı tamamen kapatın
2. Konfigürasyon dosyasını kaydedin
3. Claude Desktop'ı yeniden açın

### Adım 3: Claude ile Test Edin
Claude Desktop'ta şu komutları deneyin:
- "Ekranımı yakalayabilir misin?"
- "Mevcut monitörleri listele"
- "Ekran akışını başlat"
- "Sunucu durumunu kontrol et"

## 🔍 Sorun Giderme

### Yaygın Sorunlar ve Çözümleri

#### 1. "Command not found" Hatası
```bash
# PATH'i kontrol edin
echo $PATH

# Pip kurulum dizinini bulun
pip show -f base64-streaming-mcp

# Manuel olarak PATH'e ekleyin
export PATH="$HOME/.local/bin:$PATH"
```

#### 2. İzin Hataları (macOS)
```bash
# Ekran kaydı izni verin:
# System Preferences > Security & Privacy > Privacy > Screen Recording
# Claude Desktop ve Terminal'i listeye ekleyin
```

#### 3. Port Çakışması
```bash
# Farklı port kullanın
base64-streaming-mcp --port 8081

# Veya kullanılan portları kontrol edin
netstat -an | grep 8080
```

#### 4. Python Versiyon Sorunu
```bash
# Python versiyonunu kontrol edin
python --version

# Gerekirse Python 3.7+ kurun
# Windows: python.org'dan indirin
# macOS: brew install python
# Linux: sudo apt install python3.9
```

### Log Kontrolü
```bash
# Debug modunda çalıştırın
base64-streaming-mcp --log-level DEBUG

# Log dosyasını kontrol edin (varsa)
tail -f ~/.local/share/base64-streaming-mcp/logs/server.log
```

## 📞 Destek

Sorun yaşıyorsanız:
1. Bu rehberi tekrar kontrol edin
2. GitHub Issues'da arama yapın
3. Yeni issue oluşturun
4. Log dosyalarını paylaşın

## 🎯 Performans Optimizasyonu

### Düşük Bant Genişliği için
```bash
base64-streaming-mcp --fps 10 --quality 60 --compression gzip
```

### Yüksek Kalite için
```bash
base64-streaming-mcp --fps 60 --quality 95 --compression zstd
```

### Genel Kullanım için
```bash
base64-streaming-mcp --fps 30 --quality 85 --compression gzip
```
