# MANIFEST.in for base64-streaming-mcp package
# This file specifies which additional files to include in the source distribution

# Include essential documentation files
include README.md
include LICENSE
include CHANGELOG.md
include CONTRIBUTING.md

# Include configuration files
include config.json
include claude_desktop_config.json
include pyproject.toml
include setup.py

# Include requirements files
include requirements.txt
include requirements-dev.txt

# Include example files
recursive-include examples *.py
recursive-include examples *.json
recursive-include examples *.md

# Include test files
recursive-include tests *.py
recursive-include tests *.json
recursive-include tests *.yaml
recursive-include tests *.yml

# Include source package data
recursive-include src *.py
recursive-include src *.json
recursive-include src *.yaml
recursive-include src *.yml

# Include configuration templates and schemas
recursive-include src/config *.json
recursive-include src/config *.yaml
recursive-include src/config *.yml
recursive-include src/config *.schema.json

# Include documentation
recursive-include docs *.md
recursive-include docs *.rst
recursive-include docs *.txt
recursive-include docs *.png
recursive-include docs *.jpg
recursive-include docs *.svg

# Include scripts
include run_server.py
include scripts/*.py
include scripts/*.sh
include scripts/*.bat

# Include Docker files
include Dockerfile
include docker-compose.yml
include .dockerignore

# Include CI/CD configuration
include .github/workflows/*.yml
include .github/workflows/*.yaml
include .gitlab-ci.yml
include .travis.yml

# Include development tools configuration
include .pre-commit-config.yaml
include .gitignore
include .editorconfig
include tox.ini

# Exclude unnecessary files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .DS_Store
global-exclude *.so
global-exclude .coverage
global-exclude .pytest_cache
global-exclude .mypy_cache
global-exclude .tox
global-exclude build
global-exclude dist
global-exclude *.egg-info

# Exclude IDE files
global-exclude .vscode
global-exclude .idea
global-exclude *.swp
global-exclude *.swo
global-exclude *~

# Exclude temporary files
global-exclude *.tmp
global-exclude *.temp
global-exclude *.log

# Exclude environment files
global-exclude .env
global-exclude .env.local
global-exclude .env.*.local

# Exclude backup files
global-exclude *.bak
global-exclude *.backup
