"""
Streamable HTTP Transport Layer for MCP

This module implements the Streamable HTTP protocol for Model Context Protocol (MCP),
providing efficient HTTP/2-based streaming with chunked transfer encoding.

Features:
- HTTP/2 multiplexing support
- Chunked transfer encoding for real-time streaming
- JSON-RPC 2.0 message handling over HTTP
- Server-Sent Events (SSE) for push notifications
- Compression and optimization
"""

import asyncio
import json
import time
import gzip
import zlib
from typing import Dict, Any, Optional, AsyncGenerator, Callable, Set
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime
import uuid

import structlog
from aiohttp import web, WSMsgType, ClientSession
from aiohttp.web_response import StreamResponse
from aiohttp.web_request import Request

logger = structlog.get_logger(__name__)


class StreamableHTTPError(Exception):
    """Base exception for Streamable HTTP transport errors."""
    pass


class TransportState(str, Enum):
    """Transport connection state."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    STREAMING = "streaming"
    ERROR = "error"


@dataclass
class HTTPStreamMessage:
    """HTTP stream message structure."""
    id: str
    type: str
    data: Any
    timestamp: float
    compression: Optional[str] = None
    
    def to_json(self) -> str:
        """Convert message to JSON string."""
        return json.dumps(asdict(self))
    
    @classmethod
    def from_json(cls, json_str: str) -> 'HTTPStreamMessage':
        """Create message from JSON string."""
        data = json.loads(json_str)
        return cls(**data)


@dataclass
class StreamStats:
    """Streaming statistics."""
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    errors: int = 0
    start_time: float = 0.0
    last_activity: float = 0.0
    
    def get_duration(self) -> float:
        """Get connection duration in seconds."""
        return time.time() - self.start_time if self.start_time > 0 else 0.0
    
    def get_throughput(self) -> Dict[str, float]:
        """Get throughput statistics."""
        duration = self.get_duration()
        if duration == 0:
            return {"messages_per_sec": 0.0, "bytes_per_sec": 0.0}
        
        return {
            "messages_per_sec": self.messages_sent / duration,
            "bytes_per_sec": self.bytes_sent / duration
        }


class StreamableHTTPTransport:
    """Streamable HTTP transport implementation for MCP."""
    
    def __init__(self, host: str = "localhost", port: int = 7789):
        """Initialize Streamable HTTP transport."""
        self.host = host
        self.port = port
        self.app = web.Application()
        self.state = TransportState.DISCONNECTED
        self.stats = StreamStats()
        
        # Connection management
        self.active_connections: Dict[str, StreamResponse] = {}
        self.message_handlers: Dict[str, Callable] = {}
        self.stream_handlers: Dict[str, Callable] = {}
        
        # Setup routes
        self._setup_routes()
        
        logger.info(
            "Streamable HTTP transport initialized",
            host=self.host,
            port=self.port
        )
    
    def _setup_routes(self):
        """Setup HTTP routes for MCP protocol."""
        # MCP Protocol endpoints
        self.app.router.add_post('/mcp/message', self._handle_mcp_message)
        self.app.router.add_get('/mcp/stream', self._handle_mcp_stream)
        
        # Streaming endpoints
        self.app.router.add_get('/stream/screen', self._handle_screen_stream)
        self.app.router.add_get('/stream/events', self._handle_event_stream)
        
        # Health and status
        self.app.router.add_get('/health', self._handle_health)
        self.app.router.add_get('/status', self._handle_status)
        
        # CORS support
        self.app.router.add_options('/{path:.*}', self._handle_options)
    
    async def _handle_options(self, request: Request) -> web.Response:
        """Handle CORS preflight requests."""
        return web.Response(
            headers={
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                'Access-Control-Max-Age': '86400'
            }
        )
    
    async def _handle_health(self, request: Request) -> web.Response:
        """Handle health check requests."""
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "transport_state": self.state.value,
            "active_connections": len(self.active_connections),
            "stats": asdict(self.stats)
        }
        return web.json_response(health_data)
    
    async def _handle_status(self, request: Request) -> web.Response:
        """Handle status requests."""
        status_data = {
            "transport": {
                "type": "streamable_http",
                "version": "1.0.0",
                "state": self.state.value,
                "host": self.host,
                "port": self.port
            },
            "connections": {
                "active": len(self.active_connections),
                "total_handled": self.stats.messages_received
            },
            "performance": self.stats.get_throughput(),
            "uptime": self.stats.get_duration()
        }
        return web.json_response(status_data)
    
    async def _handle_mcp_message(self, request: Request) -> web.Response:
        """Handle MCP JSON-RPC messages over HTTP."""
        try:
            # Parse JSON-RPC message
            message_data = await request.json()
            
            # Create stream message
            stream_msg = HTTPStreamMessage(
                id=str(uuid.uuid4()),
                type="mcp_request",
                data=message_data,
                timestamp=time.time()
            )
            
            # Process message
            if "method" in message_data:
                method = message_data["method"]
                if method in self.message_handlers:
                    result = await self.message_handlers[method](message_data)
                    response_data = {
                        "jsonrpc": "2.0",
                        "id": message_data.get("id"),
                        "result": result
                    }
                else:
                    response_data = {
                        "jsonrpc": "2.0",
                        "id": message_data.get("id"),
                        "error": {
                            "code": -32601,
                            "message": f"Method not found: {method}"
                        }
                    }
            else:
                response_data = {
                    "jsonrpc": "2.0",
                    "id": message_data.get("id"),
                    "error": {
                        "code": -32600,
                        "message": "Invalid Request"
                    }
                }
            
            # Update stats
            self.stats.messages_received += 1
            self.stats.last_activity = time.time()
            
            return web.json_response(response_data)
            
        except Exception as e:
            self.stats.errors += 1
            logger.error("Error handling MCP message", error=str(e), exc_info=True)
            
            error_response = {
                "jsonrpc": "2.0",
                "id": None,
                "error": {
                    "code": -32603,
                    "message": "Internal error"
                }
            }
            return web.json_response(error_response, status=500)
    
    async def _handle_mcp_stream(self, request: Request) -> StreamResponse:
        """Handle MCP streaming connections."""
        connection_id = str(uuid.uuid4())
        
        # Create streaming response
        response = StreamResponse(
            status=200,
            reason='OK',
            headers={
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Transfer-Encoding': 'chunked'
            }
        )
        
        await response.prepare(request)
        
        # Register connection
        self.active_connections[connection_id] = response
        
        try:
            logger.info("MCP stream connection established", connection_id=connection_id)
            
            # Send initial connection message
            init_msg = HTTPStreamMessage(
                id=str(uuid.uuid4()),
                type="connection_established",
                data={"connection_id": connection_id},
                timestamp=time.time()
            )
            
            await self._send_stream_message(response, init_msg)
            
            # Keep connection alive
            while not response.task.done():
                await asyncio.sleep(1)
                
                # Send heartbeat
                heartbeat_msg = HTTPStreamMessage(
                    id=str(uuid.uuid4()),
                    type="heartbeat",
                    data={"timestamp": time.time()},
                    timestamp=time.time()
                )
                
                await self._send_stream_message(response, heartbeat_msg)
                
        except Exception as e:
            logger.error("Error in MCP stream", connection_id=connection_id, error=str(e))
        finally:
            # Clean up connection
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
            
            logger.info("MCP stream connection closed", connection_id=connection_id)
        
        return response
    
    async def _send_stream_message(self, response: StreamResponse, message: HTTPStreamMessage):
        """Send message over HTTP stream."""
        try:
            # Convert to JSON
            json_data = message.to_json()
            
            # Add compression if needed
            if message.compression == "gzip":
                json_data = gzip.compress(json_data.encode()).decode('latin1')
            
            # Send as HTTP chunk
            chunk_data = f"data: {json_data}\n\n"
            await response.write(chunk_data.encode())
            
            # Update stats
            self.stats.messages_sent += 1
            self.stats.bytes_sent += len(chunk_data)
            self.stats.last_activity = time.time()
            
        except Exception as e:
            self.stats.errors += 1
            logger.error("Error sending stream message", error=str(e))
            raise
    
    def register_message_handler(self, method: str, handler: Callable):
        """Register handler for MCP method."""
        self.message_handlers[method] = handler
        logger.info("Registered MCP message handler", method=method)
    
    def register_stream_handler(self, stream_type: str, handler: Callable):
        """Register handler for stream type."""
        self.stream_handlers[stream_type] = handler
        logger.info("Registered stream handler", stream_type=stream_type)
    
    async def start(self):
        """Start the HTTP transport server."""
        try:
            self.state = TransportState.CONNECTING
            self.stats.start_time = time.time()
            
            runner = web.AppRunner(self.app)
            await runner.setup()
            
            site = web.TCPSite(runner, self.host, self.port)
            await site.start()
            
            self.state = TransportState.CONNECTED
            
            logger.info(
                "Streamable HTTP transport started",
                host=self.host,
                port=self.port,
                state=self.state.value
            )
            
        except Exception as e:
            self.state = TransportState.ERROR
            logger.error("Failed to start HTTP transport", error=str(e), exc_info=True)
            raise StreamableHTTPError(f"Failed to start transport: {e}")
    
    async def stop(self):
        """Stop the HTTP transport server."""
        try:
            self.state = TransportState.DISCONNECTED
            
            # Close all active connections
            for connection_id, response in self.active_connections.items():
                try:
                    await response.write_eof()
                except:
                    pass
            
            self.active_connections.clear()
            
            logger.info("Streamable HTTP transport stopped")
            
        except Exception as e:
            logger.error("Error stopping HTTP transport", error=str(e))
    
    async def _handle_screen_stream(self, request: Request) -> StreamResponse:
        """Handle screen capture streaming."""
        connection_id = str(uuid.uuid4())

        # Create streaming response for screen data
        response = StreamResponse(
            status=200,
            reason='OK',
            headers={
                'Content-Type': 'application/octet-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Transfer-Encoding': 'chunked'
            }
        )

        await response.prepare(request)

        try:
            logger.info("Screen stream connection established", connection_id=connection_id)

            # Check if screen stream handler is registered
            if "screen" in self.stream_handlers:
                async for frame_data in self.stream_handlers["screen"]():
                    # Send frame as HTTP chunk
                    chunk_header = f"{len(frame_data):x}\r\n".encode()
                    chunk_data = frame_data + b"\r\n"

                    await response.write(chunk_header + chunk_data)

                    # Update stats
                    self.stats.bytes_sent += len(chunk_data)
                    self.stats.last_activity = time.time()
            else:
                # Send error message
                error_msg = b"Screen stream handler not registered"
                await response.write(error_msg)

        except Exception as e:
            logger.error("Error in screen stream", connection_id=connection_id, error=str(e))
        finally:
            logger.info("Screen stream connection closed", connection_id=connection_id)

        return response

    async def _handle_event_stream(self, request: Request) -> StreamResponse:
        """Handle Server-Sent Events stream."""
        connection_id = str(uuid.uuid4())

        # Create SSE response
        response = StreamResponse(
            status=200,
            reason='OK',
            headers={
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            }
        )

        await response.prepare(request)

        # Register for events
        self.active_connections[connection_id] = response

        try:
            logger.info("Event stream connection established", connection_id=connection_id)

            # Send initial event
            await response.write(f"data: {json.dumps({'type': 'connected', 'id': connection_id})}\n\n".encode())

            # Keep connection alive
            while not response.task.done():
                await asyncio.sleep(30)  # Send heartbeat every 30 seconds
                await response.write(f"data: {json.dumps({'type': 'heartbeat', 'timestamp': time.time()})}\n\n".encode())

        except Exception as e:
            logger.error("Error in event stream", connection_id=connection_id, error=str(e))
        finally:
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
            logger.info("Event stream connection closed", connection_id=connection_id)

        return response

    async def broadcast_message(self, message: HTTPStreamMessage):
        """Broadcast message to all active connections."""
        if not self.active_connections:
            return

        failed_connections = []

        for connection_id, response in self.active_connections.items():
            try:
                await self._send_stream_message(response, message)
            except Exception as e:
                logger.error(
                    "Failed to send to connection",
                    connection_id=connection_id,
                    error=str(e)
                )
                failed_connections.append(connection_id)

        # Clean up failed connections
        for connection_id in failed_connections:
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
