#!/usr/bin/env python3
"""
Test script to verify the package installation and functionality
"""

import sys
import subprocess
import importlib.util
from pathlib import Path


def test_package_import():
    """Test if the package can be imported correctly."""
    print("🔍 Testing package import...")
    
    try:
        import src
        print(f"✅ Package imported successfully. Version: {src.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import package: {e}")
        return False


def test_cli_entry_points():
    """Test if CLI entry points are working."""
    print("\n🔍 Testing CLI entry points...")
    
    commands = [
        "base64-streaming-mcp --help",
        "streamable-mcp --help", 
        "mcp-screen-server --help"
    ]
    
    success_count = 0
    for cmd in commands:
        try:
            result = subprocess.run(
                cmd.split(), 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode == 0:
                print(f"✅ {cmd.split()[0]} command works")
                success_count += 1
            else:
                print(f"❌ {cmd.split()[0]} command failed: {result.stderr}")
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            print(f"❌ {cmd.split()[0]} command not found or timeout: {e}")
    
    return success_count == len(commands)


def test_dependencies():
    """Test if all required dependencies are available."""
    print("\n🔍 Testing dependencies...")
    
    required_packages = [
        "mcp", "aiohttp", "aiofiles", "websockets", "mss", 
        "PIL", "asyncio_mqtt", "jsonrpc_base", "pydantic", 
        "structlog", "psutil", "zstandard", "h2"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == "PIL":
                import PIL
            elif package == "asyncio_mqtt":
                import asyncio_mqtt
            elif package == "jsonrpc_base":
                import jsonrpc_base
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        return False
    
    print("✅ All dependencies are available")
    return True


def test_module_structure():
    """Test if the module structure is correct."""
    print("\n🔍 Testing module structure...")
    
    try:
        from src.main import main, cli_main
        from src.config import get_config
        from src.streamable_http_server import StreamableHTTPMCPServer
        from src.screen_capture import ScreenCapture
        
        print("✅ All main modules can be imported")
        return True
    except ImportError as e:
        print(f"❌ Module import failed: {e}")
        return False


def test_configuration():
    """Test if configuration loading works."""
    print("\n🔍 Testing configuration...")
    
    try:
        from src.config import get_config, get_default_config
        
        config = get_default_config()
        print(f"✅ Default config loaded: {config.host}:{config.port}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def run_installation_test():
    """Run all installation tests."""
    print("🚀 Base64 Streaming MCP - Installation Test")
    print("=" * 50)
    
    tests = [
        ("Package Import", test_package_import),
        ("Dependencies", test_dependencies),
        ("Module Structure", test_module_structure),
        ("Configuration", test_configuration),
        ("CLI Entry Points", test_cli_entry_points),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} test failed")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Package is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the installation.")
        return False


if __name__ == "__main__":
    success = run_installation_test()
    sys.exit(0 if success else 1)
