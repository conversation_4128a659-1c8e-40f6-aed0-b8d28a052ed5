#!/usr/bin/env python3
"""
Setup script for base64-streaming-mcp package
"""

import os
import sys
from pathlib import Path
from setuptools import setup, find_packages

# Ensure we're in the right directory
here = Path(__file__).parent.absolute()

# Read the README file
readme_path = here / "README.md"
long_description = ""
if readme_path.exists():
    with open(readme_path, "r", encoding="utf-8") as f:
        long_description = f.read()

# Read requirements from requirements.txt
requirements_path = here / "requirements.txt"
install_requires = []
dev_requires = []

if requirements_path.exists():
    with open(requirements_path, "r", encoding="utf-8") as f:
        lines = f.readlines()
        
    for line in lines:
        line = line.strip()
        if line and not line.startswith("#"):
            # Separate dev dependencies
            if any(dev_pkg in line.lower() for dev_pkg in ["pytest", "black", "flake8", "mypy"]):
                dev_requires.append(line)
            else:
                install_requires.append(line)

# Read version from src/__init__.py
version_path = here / "src" / "__init__.py"
version = "2.0.0"  # fallback
if version_path.exists():
    with open(version_path, "r", encoding="utf-8") as f:
        for line in f:
            if line.startswith("__version__"):
                version = line.split("=")[1].strip().strip('"').strip("'")
                break

# Package metadata
PACKAGE_NAME = "base64-streaming-mcp"
PACKAGE_DESCRIPTION = "Streamable HTTP MCP Server for real-time screen streaming"
AUTHOR = "inkbytefo"
AUTHOR_EMAIL = "<EMAIL>"
URL = "https://github.com/inkbytefo/base64-streaming-mcp"
LICENSE = "MIT"

# Classifiers
CLASSIFIERS = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Multimedia :: Graphics :: Capture :: Screen Capture",
    "Topic :: System :: Networking",
    "Framework :: AsyncIO",
]

# Keywords
KEYWORDS = [
    "mcp", "model-context-protocol", "screen-capture", "streaming", 
    "http", "real-time", "base64", "asyncio", "server"
]

# Entry points for CLI
ENTRY_POINTS = {
    "console_scripts": [
        "base64-streaming-mcp=src.main:cli_main",
        "streamable-mcp=src.main:cli_main",
        "mcp-screen-server=src.main:cli_main",
    ],
}

# Extra requirements
EXTRAS_REQUIRE = {
    "dev": dev_requires + [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "black>=23.0.0",
        "flake8>=6.0.0",
        "mypy>=1.0.0",
        "pre-commit>=3.0.0",
    ],
    "performance": [
        "psutil>=5.9.0",
        "zstandard>=0.21.0",
        "h2>=4.1.0",
    ],
    "monitoring": [
        "prometheus-client>=0.16.0",
        "grafana-api>=1.0.3",
    ],
    "all": [],  # Will be populated below
}

# Populate 'all' extra
EXTRAS_REQUIRE["all"] = list(set(
    sum(EXTRAS_REQUIRE.values(), [])
))

# Package data to include
PACKAGE_DATA = {
    "src": [
        "*.json",
        "*.yaml",
        "*.yml",
        "config/*.json",
        "config/*.yaml",
        "config/*.yml",
    ],
}

# Data files to include in the package
DATA_FILES = [
    ("share/base64-streaming-mcp/examples", ["examples/client_example.py"]),
    ("share/base64-streaming-mcp/config", ["config.json", "claude_desktop_config.json"]),
]

# Ensure data files exist before including them
filtered_data_files = []
for target_dir, files in DATA_FILES:
    existing_files = []
    for file_path in files:
        if Path(file_path).exists():
            existing_files.append(file_path)
    if existing_files:
        filtered_data_files.append((target_dir, existing_files))

setup(
    name=PACKAGE_NAME,
    version=version,
    description=PACKAGE_DESCRIPTION,
    long_description=long_description,
    long_description_content_type="text/markdown",
    author=AUTHOR,
    author_email=AUTHOR_EMAIL,
    url=URL,
    license=LICENSE,
    
    # Package discovery
    packages=find_packages(include=["src", "src.*"]),
    package_dir={"": "."},
    package_data=PACKAGE_DATA,
    data_files=filtered_data_files,
    include_package_data=True,
    
    # Dependencies
    python_requires=">=3.7",
    install_requires=install_requires,
    extras_require=EXTRAS_REQUIRE,
    
    # Entry points
    entry_points=ENTRY_POINTS,
    
    # Metadata
    classifiers=CLASSIFIERS,
    keywords=", ".join(KEYWORDS),
    project_urls={
        "Bug Reports": f"{URL}/issues",
        "Source": URL,
        "Documentation": f"{URL}#readme",
        "Changelog": f"{URL}/blob/main/CHANGELOG.md",
    },
    
    # Options
    zip_safe=False,
    platforms=["any"],
    
    # Test suite
    test_suite="tests",
    tests_require=dev_requires,
)
