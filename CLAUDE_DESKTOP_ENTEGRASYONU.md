# 🤖 <PERSON> Desktop Entegrasyonu - Det<PERSON><PERSON><PERSON> Rehber

Bu rehber, base64-streaming-mcp server'ın<PERSON> <PERSON>op ile entegre etmek için adım adım talimatlar içerir.

## 📋 <PERSON><PERSON>

- ✅ <PERSON> Desktop uygulaması kurulu
- ✅ Base64-streaming-mcp server kurulu (`pip install git+https://github.com/inkbytefo/base64-streaming-mcp.git`)
- ✅ Python 3.7+ ve pip çalı<PERSON>ır durumda
- ✅ Ekran kaydı izinleri verilmiş (macOS için önemli)

## 🔧 Claude Desktop Konfigürasyonu

### 1. Konfigürasyon Dosyasının Konumu

#### Windows
```
%APPDATA%\Claude\claude_desktop_config.json
```
**Tam yol örneği:**
```
C:\Users\<USER>\AppData\Roaming\Claude\claude_desktop_config.json
```

#### macOS
```
~/Library/Application Support/Claude/claude_desktop_config.json
```
**Tam yol <PERSON>i:**
```
/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json
```

#### Linux
```
~/.config/Claude/claude_desktop_config.json
```
**Tam yol örneği:**
```
/home/<USER>/.config/Claude/claude_desktop_config.json
```

### 2. Konfigürasyon Dosyasını Oluşturma/Düzenleme

#### Dosya Yoksa Oluşturun
```bash
# Windows (PowerShell)
New-Item -Path "$env:APPDATA\Claude\claude_desktop_config.json" -ItemType File -Force

# macOS/Linux
mkdir -p "$(dirname ~/.config/Claude/claude_desktop_config.json)"
touch ~/.config/Claude/claude_desktop_config.json
```

#### Temel Konfigürasyon
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": []
    }
  }
}
```

#### Önerilen Konfigürasyon
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": [
        "--host", "localhost",
        "--port", "8080",
        "--fps", "30",
        "--quality", "85",
        "--compression", "gzip",
        "--log-level", "INFO"
      ]
    }
  }
}
```

### 3. Platform-Specific Konfigürasyonlar

#### Windows Konfigürasyonu
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": [
        "--host", "localhost",
        "--port", "8080",
        "--fps", "25",
        "--quality", "80",
        "--compression", "gzip"
      ],
      "env": {
        "PYTHONIOENCODING": "utf-8"
      }
    }
  }
}
```

#### macOS Konfigürasyonu
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "/usr/local/bin/base64-streaming-mcp",
      "args": [
        "--host", "localhost",
        "--port", "8080",
        "--fps", "30",
        "--quality", "85",
        "--compression", "zstd"
      ],
      "env": {
        "PATH": "/usr/local/bin:/opt/homebrew/bin:$PATH"
      }
    }
  }
}
```

#### Linux Konfigürasyonu
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "/home/<USER>/.local/bin/base64-streaming-mcp",
      "args": [
        "--host", "localhost",
        "--port", "8080",
        "--fps", "30",
        "--quality", "85",
        "--compression", "gzip"
      ],
      "env": {
        "DISPLAY": ":0",
        "PATH": "/home/<USER>/.local/bin:$PATH"
      }
    }
  }
}
```

## 🚀 Kurulum Adımları

### Adım 1: MCP Server'ı Test Edin
```bash
# Sunucunun çalıştığını kontrol edin
base64-streaming-mcp --help

# Test çalıştırması
base64-streaming-mcp --host localhost --port 8080 &

# Health check
curl http://localhost:8080/health

# Sunucuyu durdurun
pkill -f base64-streaming-mcp
```

### Adım 2: Claude Desktop Konfigürasyonunu Ekleyin
1. Claude Desktop'ı kapatın
2. Konfigürasyon dosyasını düzenleyin
3. JSON formatının doğru olduğunu kontrol edin
4. Dosyayı kaydedin

### Adım 3: Claude Desktop'ı Yeniden Başlatın
1. Claude Desktop'ı tamamen kapatın (sistem tepsisinden de)
2. Birkaç saniye bekleyin
3. Claude Desktop'ı yeniden açın

### Adım 4: Bağlantıyı Test Edin
Claude Desktop'ta şu mesajları deneyin:
```
"MCP server durumunu kontrol edebilir misin?"
"Ekranımı yakalayabilir misin?"
"Mevcut monitörleri listele"
```

## 🔧 Gelişmiş Konfigürasyon Seçenekleri

### Performans Optimizasyonu
```json
{
  "mcpServers": {
    "base64-streaming-mcp-performance": {
      "command": "base64-streaming-mcp",
      "args": [
        "--host", "localhost",
        "--port", "8080",
        "--fps", "60",
        "--quality", "95",
        "--compression", "zstd"
      ]
    }
  }
}
```

### Düşük Kaynak Kullanımı
```json
{
  "mcpServers": {
    "base64-streaming-mcp-light": {
      "command": "base64-streaming-mcp",
      "args": [
        "--host", "localhost",
        "--port", "8080",
        "--fps", "15",
        "--quality", "70",
        "--compression", "gzip"
      ]
    }
  }
}
```

### Konfigürasyon Dosyası ile
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--config", "/path/to/config.json"]
    }
  }
}
```

## 🔍 Sorun Giderme

### 1. Claude Desktop MCP Server'ı Görmüyor

#### Kontrol Listesi:
- [ ] JSON formatı doğru mu?
- [ ] Dosya yolu doğru mu?
- [ ] Claude Desktop yeniden başlatıldı mı?
- [ ] Komut PATH'te mevcut mu?

#### Test:
```bash
# JSON formatını kontrol edin
python -m json.tool claude_desktop_config.json

# Komut yolunu kontrol edin
which base64-streaming-mcp
```

### 2. "Command not found" Hatası

#### Çözüm 1: Tam yol kullanın
```bash
# Komutun tam yolunu bulun
which base64-streaming-mcp
# veya
pip show -f base64-streaming-mcp
```

#### Çözüm 2: PATH'i düzeltin
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "/tam/yol/base64-streaming-mcp",
      "args": [],
      "env": {
        "PATH": "/usr/local/bin:/home/<USER>/.local/bin:$PATH"
      }
    }
  }
}
```

### 3. İzin Hataları (macOS)

#### Ekran Kaydı İzni:
1. System Preferences > Security & Privacy > Privacy
2. Screen Recording sekmesine gidin
3. Claude Desktop'ı listeye ekleyin
4. Terminal'i de ekleyin (gerekirse)

#### Accessibility İzni:
1. System Preferences > Security & Privacy > Privacy
2. Accessibility sekmesine gidin
3. Claude Desktop'ı listeye ekleyin

### 4. Port Çakışması

#### Farklı port kullanın:
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--port", "7790"]
    }
  }
}
```

#### Kullanılan portları kontrol edin:
```bash
# Windows
netstat -an | findstr 8080

# macOS/Linux
netstat -an | grep 8080
lsof -i :8080
```

## 📊 Claude Desktop ile Kullanım Örnekleri

### Temel Komutlar
```
"Ekranımı yakalayabilir misin?"
"Mevcut ekran görüntüsünü al"
"Monitör listesini göster"
"Ekran akışını başlat"
"Sunucu durumunu kontrol et"
```

### Gelişmiş Komutlar
```
"30 FPS ile ekran akışını başlat"
"Yüksek kalitede ekran yakalama yap"
"İkinci monitörü yakala"
"Ekran akışını durdur"
"Sunucu istatistiklerini göster"
```

### Konfigürasyon Komutları
```
"Akış kalitesini artır"
"FPS'i 15'e düşür"
"Sıkıştırmayı değiştir"
"Sunucu ayarlarını güncelle"
```

## 🔄 Güncelleme ve Bakım

### MCP Server Güncelleme
```bash
# Pip ile güncelleme
pip install --upgrade git+https://github.com/inkbytefo/base64-streaming-mcp.git

# Claude Desktop'ı yeniden başlatın
```

### Konfigürasyon Yedekleme
```bash
# Konfigürasyonu yedekleyin
cp claude_desktop_config.json claude_desktop_config.json.backup

# Geri yükleme
cp claude_desktop_config.json.backup claude_desktop_config.json
```

## 📞 Destek ve Yardım

### Log Dosyaları
```bash
# Claude Desktop logları (macOS)
~/Library/Logs/Claude/

# MCP Server logları
base64-streaming-mcp --log-level DEBUG
```

### Hata Raporlama
GitHub Issues'da şunları paylaşın:
- İşletim sistemi ve versiyonu
- Python versiyonu
- Claude Desktop versiyonu
- Konfigürasyon dosyası (hassas bilgiler olmadan)
- Hata mesajları ve loglar
