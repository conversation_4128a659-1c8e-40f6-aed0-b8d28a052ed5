[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "base64-streaming-mcp"
version = "2.0.0"
description = "Streamable HTTP MCP Server for real-time screen streaming"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "inkbytefo", email = "<EMAIL>"}
]
maintainers = [
    {name = "inkbytefo", email = "<EMAIL>"}
]
keywords = [
    "mcp", 
    "model-context-protocol", 
    "screen-capture", 
    "streaming", 
    "http", 
    "real-time", 
    "base64", 
    "asyncio", 
    "server"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Multimedia :: Graphics :: Capture :: Screen Capture",
    "Topic :: System :: Networking",
    "Framework :: AsyncIO",
]
requires-python = ">=3.7"
dependencies = [
    "mcp>=1.0.0",
    "aiohttp>=3.9.0",
    "aiofiles>=23.0.0",
    "websockets>=12.0",
    "mss>=9.0.1",
    "Pillow>=10.0.0",
    "asyncio-mqtt>=0.16.1",
    "jsonrpc-base>=2.2.0",
    "pydantic>=2.0.0",
    "structlog>=23.0.0",
    "psutil>=5.9.0",
    "zstandard>=0.21.0",
    "h2>=4.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
    "coverage>=7.0.0",
    "pytest-cov>=4.0.0",
]
performance = [
    "psutil>=5.9.0",
    "zstandard>=0.21.0",
    "h2>=4.1.0",
    "uvloop>=0.17.0; sys_platform != 'win32'",
]
monitoring = [
    "prometheus-client>=0.16.0",
    "grafana-api>=1.0.3",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/inkbytefo/base64-streaming-mcp"
Repository = "https://github.com/inkbytefo/base64-streaming-mcp"
Documentation = "https://github.com/inkbytefo/base64-streaming-mcp#readme"
"Bug Reports" = "https://github.com/inkbytefo/base64-streaming-mcp/issues"
Changelog = "https://github.com/inkbytefo/base64-streaming-mcp/blob/main/CHANGELOG.md"

[project.scripts]
base64-streaming-mcp = "src.main:cli_main"
streamable-mcp = "src.main:cli_main"
mcp-screen-server = "src.main:cli_main"

[tool.setuptools]
package-dir = {"" = "."}
include-package-data = true

[tool.setuptools.packages.find]
include = ["src*"]
exclude = ["tests*", "docs*", "examples*"]

[tool.setuptools.package-data]
src = ["*.json", "*.yaml", "*.yml", "config/*.json", "config/*.yaml", "config/*.yml"]

[tool.black]
line-length = 88
target-version = ['py37', 'py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".tox",
]

[tool.mypy]
python_version = "3.7"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "mss.*",
    "asyncio_mqtt.*",
    "jsonrpc_base.*",
    "zstandard.*",
    "h2.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/.*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
