# Base64 Streaming MCP - <PERSON><PERSON><PERSON> Rehberi

Bu rehber, base64-streaming-mcp paketinin pip ile kurulabilir hale getirilmesi ve kurulumu için gerekli adımları açıklar.

## 📦 Pip Paketi Olarak Kurulum

### Geliştirme Modunda Kurulum (Önerilen)

```bash
# Proje dizinine gidin
cd base64-streaming-mcp

# Geliştirme modunda kurun
pip install -e .

# Veya tüm bağımlılıklarla birlikte
pip install -e ".[dev,performance,monitoring]"
```

### <PERSON><PERSON><PERSON><PERSON>

```bash
# GitHub'dan doğrudan kurulum
pip install git+https://github.com/inkbytefo/base64-streaming-mcp.git

# Yerel dizinden kurulum
pip install .

# Wheel paketi oluşturup kurulum
python setup.py bdist_wheel
pip install dist/base64_streaming_mcp-2.0.0-py3-none-any.whl
```

## 🚀 CLI Komutları

Kurulum sonrası aşağıdaki komutlar kullanılabilir:

```bash
# Ana komut
base64-streaming-mcp --help

# Alternatif komutlar
streamable-mcp --help
mcp-screen-server --help

# Sunucuyu başlatma
base64-streaming-mcp --host localhost --port 8080 --fps 30

# Konfigürasyon dosyası ile
base64-streaming-mcp --config config.json
```

## 🔧 Paket Geliştirme

### Paket Oluşturma

```bash
# Source distribution oluşturma
python setup.py sdist

# Wheel paketi oluşturma
python setup.py bdist_wheel

# Her ikisini birden
python setup.py sdist bdist_wheel

# Modern yöntem (pyproject.toml kullanarak)
pip install build
python -m build
```

### PyPI'ye Yükleme

```bash
# Test PyPI'ye yükleme
pip install twine
twine upload --repository testpypi dist/*

# Ana PyPI'ye yükleme
twine upload dist/*
```

## 📋 Kurulum Testi

Kurulumun doğru çalıştığını test etmek için:

```bash
# Test scriptini çalıştırın
python test_installation.py

# Manuel test
python -c "import src; print(src.__version__)"

# CLI test
base64-streaming-mcp --version
```

## 🏗️ Proje Yapısı Önerileri

### Mevcut Yapı
```
base64-streaming-mcp/
├── src/                    # Ana kaynak kod
│   ├── __init__.py
│   ├── main.py            # CLI entry point
│   ├── config.py
│   ├── screen_capture.py
│   ├── streamable_http_server.py
│   ├── protocol/
│   ├── transport/
│   └── streaming/
├── tests/                 # Test dosyaları
├── examples/              # Örnek kullanımlar
├── setup.py              # Kurulum scripti
├── pyproject.toml        # Modern packaging
├── MANIFEST.in           # Paket içeriği
├── requirements.txt      # Bağımlılıklar
└── README.md            # Dokümantasyon
```

### Önerilen İyileştirmeler

1. **Paket Adı Standardizasyonu:**
   ```bash
   # Mevcut: base64-streaming-mcp
   # Önerilen: base64_streaming_mcp (Python package naming)
   ```

2. **Versiyon Yönetimi:**
   ```python
   # src/__init__.py içinde tek bir yerde versiyon tanımı
   __version__ = "2.0.0"
   
   # setup.py ve pyproject.toml'da dinamik versiyon okuma
   ```

3. **Bağımlılık Ayrımı:**
   ```
   requirements/
   ├── base.txt          # Temel bağımlılıklar
   ├── dev.txt           # Geliştirme bağımlılıkları
   ├── performance.txt   # Performans bağımlılıkları
   └── monitoring.txt    # İzleme bağımlılıkları
   ```

4. **Konfigürasyon Yönetimi:**
   ```
   src/
   └── config/
       ├── __init__.py
       ├── defaults.json
       ├── schema.json
       └── examples/
   ```

5. **Dokümantasyon Yapısı:**
   ```
   docs/
   ├── installation.md
   ├── configuration.md
   ├── api.md
   ├── examples/
   └── troubleshooting.md
   ```

## 🔍 Sorun Giderme

### Yaygın Kurulum Sorunları

1. **Bağımlılık Çakışmaları:**
   ```bash
   # Sanal ortam kullanın
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   venv\Scripts\activate     # Windows
   pip install -e .
   ```

2. **Permission Hataları:**
   ```bash
   # User-level kurulum
   pip install --user -e .
   
   # Veya sudo kullanın (önerilmez)
   sudo pip install -e .
   ```

3. **Platform-Specific Sorunlar:**
   ```bash
   # macOS: Screen recording izni gerekli
   # Windows: Admin yetkileri gerekebilir
   # Linux: X11 bağımlılıkları
   ```

### Kurulum Doğrulama

```bash
# Paket bilgilerini kontrol edin
pip show base64-streaming-mcp

# Kurulu dosyaları listeleyin
pip show -f base64-streaming-mcp

# Bağımlılıkları kontrol edin
pip check base64-streaming-mcp
```

## 📚 Ek Kaynaklar

- [Python Packaging Guide](https://packaging.python.org/)
- [setuptools Documentation](https://setuptools.pypa.io/)
- [PyPI Upload Guide](https://packaging.python.org/tutorials/packaging-projects/)
- [Semantic Versioning](https://semver.org/)

## 🤝 Katkıda Bulunma

1. Repository'yi fork edin
2. Feature branch oluşturun
3. Değişikliklerinizi yapın
4. Testleri çalıştırın
5. Pull request gönderin

```bash
# Geliştirme ortamı kurulumu
git clone https://github.com/inkbytefo/base64-streaming-mcp.git
cd base64-streaming-mcp
pip install -e ".[dev]"
pre-commit install
```
